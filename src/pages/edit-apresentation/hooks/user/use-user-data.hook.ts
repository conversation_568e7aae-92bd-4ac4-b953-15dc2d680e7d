interface IUserData {
  name: string;
  email: string;
  avatar: string;
}

interface IUseUserDataHook {
  user: IUserData;
  isLoading: boolean;
  error: string | null;
}

// TODO: Implement real user data fetching from authentication context
export const useUserData = (): IUseUserDataHook => {
  // Mock data - replace with real authentication logic
  const mockUser: IUserData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150',
  };

  return {
    user: mockUser,
    isLoading: false,
    error: null,
  };
};
