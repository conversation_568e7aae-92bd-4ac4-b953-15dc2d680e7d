import React from 'react';
import { KonvaEventObject } from 'konva/lib/Node';

export interface ICanvasEditorProps {
	propertiesPanelRef: React.RefObject<HTMLDivElement>;
}

export interface ICanvasEditorConfig {
	containerRef: React.RefObject<HTMLDivElement>;
	stageRef: React.RefObject<any>; // Substitua por React.RefObject<Konva.Stage> se possível
	transformerRef: React.RefObject<any>; // Substitua por React.RefObject<Konva.Transformer> se possível
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	padding: number;
}

export interface ICanvasEditorInteractions {
	handleCanvasClick: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseMove: (e: KonvaEventObject<MouseEvent>) => void;
	handleSelectShape: (params: { idElement: string; event: KonvaEventObject<MouseEvent> }) => void;
	handleDragEnd: (e: KonvaEventObject<DragEvent>) => void;
	handleTransformEnd: (e: KonvaEventObject<Event>) => void;
	handleDragStart: (e: KonvaEventObject<DragEvent>) => void;
	handleDragMove: (e: KonvaEventObject<DragEvent>) => void;
	handleMouseLeave: () => void;
	handleTransformStart: (e: KonvaEventObject<Event>) => void;
	handleTransform: (e: KonvaEventObject<Event>) => void;
	hoverPosition: { x: number; y: number } | null;
}

export interface ISelectionBoxConfig {
	selectionBox: any;
	handleMouseDown: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseMoveSelectionBox: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseUp: (e: KonvaEventObject<MouseEvent>) => void;
}

export interface IShapeHoverConfig {
	hoveredShape: {
		item: any; // Tipar conforme implementação
		x: number;
		y: number;
	};
	handleShapeMouseEnter: (item: any, x: number, y: number) => void; // Tipar item
	handleShapeMouseLeave: () => void;
	clearHoveredShape: () => void;
}
