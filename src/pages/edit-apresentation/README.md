# Edit Apresentation Module

Este módulo contém toda a funcionalidade do editor de apresentações, seguindo princípios SOLID e arquitetura limpa.

## 📁 Estrutura de Pastas

```
src/pages/edit-apresentation/
├── components/          # Componentes React reutilizáveis
├── data/               # Constantes, configurações e dados estáticos
├── dtos/               # Data Transfer Objects
├── hooks/              # Custom hooks para lógica de negócio
├── lib/                # Utilitários e funções auxiliares
├── services/           # Camada de serviços e API
├── states/             # Estados globais (Jotai atoms)
├── types/              # Definições de tipos TypeScript
└── index.tsx           # Componente principal
```

## 🎯 Princípios Seguidos

### SOLID
- **Single Responsibility**: Cada hook/componente tem uma única responsabilidade
- **Open/Closed**: Extensível através de composição
- **Liskov Substitution**: Interfaces bem definidas
- **Interface Segregation**: Interfaces específicas para cada uso
- **Dependency Inversion**: Dependências injetadas via props/hooks

### Clean Architecture
- **Separação de camadas**: UI, lógica de negócio, dados
- **Inversão de dependências**: Hooks abstraem a lógica
- **Testabilidade**: Lógica isolada em hooks

## 🔧 Hooks Principais

### Canvas
- `useCanvasEditor`: Hook principal do editor
- `useCanvasConfig`: Configurações do canvas
- `useCanvasInteractions`: Interações do usuário

### Elements
- `useManageElement`: Gerenciamento de elementos
- `useElementLayer`: Camadas de elementos
- `useItemSelection`: Seleção de itens

### User
- `useUserData`: Dados do usuário autenticado

## 📝 Convenções

### Nomenclatura
- Interfaces: `I<Ação><Nome><Escopo>` (ex: `ICanvasEditorProps`)
- Hooks: `use<Nome><Funcionalidade>.hook.ts`
- Componentes: PascalCase
- Constantes: UPPER_SNAKE_CASE

### Estrutura de Arquivos
- Cada pasta tem um `index.ts` para barrel exports
- Tipos são centralizados na pasta `types/`
- Constantes são organizadas por domínio

## 🚀 Como Usar

```tsx
import { PresentationEditor } from './index';

// O componente principal já está configurado
<PresentationEditor />
```

## 🔄 Próximas Melhorias

1. Implementar autenticação real no `useUserData`
2. Adicionar testes unitários para hooks
3. Implementar lazy loading para componentes pesados
4. Adicionar documentação JSDoc para hooks complexos
5. Implementar error boundaries específicos por funcionalidade
