import { useAtomValue } from 'jotai';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { nextElementAtom } from '@/shared/states/items/next-items.state';
import { ICanvasEditorConfig, ICanvasEditorInteractions } from '../../../types/canvas-editor.types';
import { IItem } from '@/pages/edit-apresentation/types';

interface IUseCanvasEditorStateHook {
  config: ICanvasEditorConfig;
  interactions: ICanvasEditorInteractions;
  items: IItem[];
  movingItem: any;
  nextCanvasElement: any;
}

export const useCanvasEditorState = (
  config: ICanvasEditorConfig,
  interactions: ICanvasEditorInteractions
): IUseCanvasEditorStateHook => {
  const items = useAtomValue(itemsAtom);
  const movingItem = items.find((item) => item.isDragging);
  const nextCanvasElement = useAtomValue(nextElementAtom);

  return {
    config,
    interactions,
    items,
    movingItem,
    nextCanvasElement,
  };
};
